/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 变量定义 */
:root {
    --primary-color: #FF4B2B;
    --secondary-color: #00C9A7;
    --accent-color: #FFD700;
    --background-color: #FFF5F5;
    --text-color: #2C3E50;
    --container-width: 1200px;
    --spacing-unit: 1rem;
    --card-shadow: 0 10px 20px rgba(0,0,0,0.1);
    --hover-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础样式 */
body {
    font-family: "思源宋体", serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    background-image: 
        radial-gradient(circle at 100% 100%, rgba(255, 75, 43, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 0% 0%, rgba(0, 201, 167, 0.1) 0%, transparent 50%);
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--spacing-unit);
}

/* 导航栏样式 */
.site-header {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    transition: var(--hover-transition);
}

.logo:hover {
    transform: scale(1.05);
    color: var(--accent-color);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: var(--hover-transition);
}

.nav-links a:hover {
    color: var(--primary-color);
    background-color: rgba(255, 75, 43, 0.1);
    transform: translateY(-2px);
}

/* 英雄区域样式 */
.hero {
    background-image: 
        linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
        url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    height: 90vh;
    display: flex;
    align-items: center;
    text-align: center;
    color: white;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 75, 43, 0.3), rgba(0, 201, 167, 0.3));
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero p {
    font-size: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out 0.3s backwards;
}

/* 食谱卡片样式 */
.latest-recipes {
    padding: 6rem 0;
    background: linear-gradient(to bottom, var(--background-color), white);
}

.latest-recipes h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--primary-color);
    position: relative;
}

.latest-recipes h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: var(--accent-color);
    margin: 1rem auto;
    border-radius: 2px;
}

.recipe-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    padding: 2rem 0;
}

.recipe-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    transition: var(--hover-transition);
    box-shadow: var(--card-shadow);
    position: relative;
}

.recipe-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.recipe-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--hover-transition);
}

.recipe-card:hover img {
    transform: scale(1.1);
}

.recipe-content {
    padding: 1.5rem;
    position: relative;
}

.recipe-content h3 {
    font-size: 1.4rem;
    margin-bottom: 0.8rem;
    color: var(--text-color);
}

.cooking-time {
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cooking-time::before {
    content: '⏱️';
}

/* 订阅区域样式 */
.newsletter {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 6rem 0;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.newsletter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/pattern.png');
    opacity: 0.1;
    animation: slide 20s linear infinite;
}

.newsletter h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    position: relative;
}

.subscribe-form {
    display: flex;
    gap: 1rem;
    max-width: 600px;
    margin: 2rem auto;
    position: relative;
}

.subscribe-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: var(--hover-transition);
}

.subscribe-form input:focus {
    outline: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

.subscribe-form button {
    background-color: var(--accent-color);
    color: var(--text-color);
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--hover-transition);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.subscribe-form button:hover {
    background-color: #FFE44D;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

/* 按钮样式 */
.cta-button {
    display: inline-block;
    padding: 1rem 2.5rem;
    background-color: var(--accent-color);
    color: var(--text-color);
    text-decoration: none;
    border-radius: 30px;
    font-weight: bold;
    margin-top: 2rem;
    transition: var(--hover-transition);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.cta-button:hover {
    background-color: #FFE44D;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

/* 标签样式 */
.recipe-tags {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.tag {
    background-color: rgba(255, 75, 43, 0.1);
    color: var(--primary-color);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 特性卡片样式 */
.features {
    padding: 6rem 0;
    background-color: white;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    text-align: center;
}

.feature-card {
    padding: 2rem;
    border-radius: 16px;
    background: var(--background-color);
    transition: var(--hover-transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* 页脚样式 */
.site-footer {
    background-color: #2C3E50;
    color: white;
    padding: 4rem 0;
    text-align: center;
    position: relative;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide {
    from {
        background-position: 0 0;
    }
    to {
        background-position: 100% 100%;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
    }

    .menu-toggle span {
        display: block;
        width: 25px;
        height: 3px;
        background-color: var(--text-color);
        margin: 5px 0;
        transition: var(--hover-transition);
    }

    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .subscribe-form {
        flex-direction: column;
        padding: 0 1rem;
    }

    .subscribe-form button {
        width: 100%;
    }
}

/* 无障碍设计 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* 打印样式 */
@media print {
    .site-header,
    .newsletter,
    .site-footer {
        display: none;
    }
} 