<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时目标检测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            width: 100%;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .video-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            overflow: hidden;
            border-radius: 8px;
            background-color: #222;
        }
        
        video, canvas {
            max-width: 100%;
            height: auto;
            display: block;
        }
        
        canvas {
            position: absolute;
            top: 0;
            left: 0;
        }
        
        .detection-list {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .detection-list h2 {
            margin-bottom: 15px;
            color: #333;
            font-size: 1.2rem;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f8f8;
            font-weight: 600;
        }
        
        .status {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f8f8;
            text-align: center;
        }
        
        .status.loading {
            background-color: #fff8e1;
            color: #856404;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        
        #startButton {
            display: block;
            margin: 20px auto;
            padding: 12px 24px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        #startButton:hover {
            background-color: #45a049;
        }
        
        #startButton:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 1.5rem;
            }
            
            .container {
                padding: 15px;
            }
            
            th, td {
                padding: 8px 10px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <h1>实时目标检测应用</h1>
    
    <div class="container">
        <div id="status" class="status loading">
            加载TensorFlow.js和COCO-SSD模型中...
        </div>
        
        <button id="startButton" disabled>开始桌面检测</button>
        
        <div class="video-container">
            <video id="video" autoplay muted playsinline></video>
            <canvas id="canvas"></canvas>
        </div>
        
        <div class="detection-list">
            <h2>检测到的目标</h2>
            <table>
                <thead>
                    <tr>
                        <th>目标类别</th>
                        <th>首次检测时间</th>
                    </tr>
                </thead>
                <tbody id="detectionTableBody">
                    <!-- 检测到的目标将会在这里动态添加 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 加载TensorFlow.js和COCO-SSD模型 -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd"></script>
    
    <script>
        // 全局变量
        let model;
        let video;
        let canvas;
        let ctx;
        let detectedObjects = {};
        let isRunning = false;
        let detectionInterval;

        // 状态元素
        const statusElement = document.getElementById('status');
        const startButton = document.getElementById('startButton');
        const detectionTableBody = document.getElementById('detectionTableBody');

        // 页面加载完成后初始化
        window.addEventListener('load', async () => {
            video = document.getElementById('video');
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            
            try {
                // 加载COCO-SSD模型
                statusElement.textContent = '加载目标检测模型中...';
                model = await cocoSsd.load();
                
                statusElement.className = 'status success';
                statusElement.textContent = '模型加载成功！点击"开始桌面检测"按钮开始。';
                startButton.disabled = false;
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `模型加载失败: ${error.message}`;
                console.error('模型加载失败:', error);
            }
        });

        // 开始检测按钮事件
        startButton.addEventListener('click', async () => {
            if (isRunning) return;
            
            try {
                // 请求屏幕共享
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        cursor: 'always'
                    },
                    audio: false
                });
                
                // 设置视频源
                video.srcObject = stream;
                
                // 等待视频元数据加载
                video.onloadedmetadata = () => {
                    // 设置画布尺寸与视频相同
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    
                    // 更新状态
                    statusElement.className = 'status success';
                    statusElement.textContent = '正在进行目标检测...';
                    
                    // 开始检测
                    isRunning = true;
                    startButton.textContent = '检测进行中';
                    startButton.disabled = true;
                    
                    // 清空之前的检测结果
                    detectedObjects = {};
                    detectionTableBody.innerHTML = '';
                    
                    // 设置检测间隔 (2fps)
                    detectObjects();
                    detectionInterval = setInterval(detectObjects, 500);
                    
                    // 监听屏幕共享结束
                    stream.getVideoTracks()[0].onended = stopDetection;
                };
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `无法访问桌面屏幕: ${error.message}`;
                console.error('屏幕共享访问失败:', error);
            }
        });

        // 目标检测函数
        async function detectObjects() {
            if (!isRunning || !model || !video.videoWidth) return;
            
            try {
                // 在画布上绘制当前视频帧
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                // 使用模型进行目标检测
                const predictions = await model.detect(video);
                
                // 清除上一帧的检测结果
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                // 标记当前时间
                const currentTime = new Date();
                
                // 绘制检测到的物体边界框
                predictions.forEach(prediction => {
                    // 提取预测数据
                    const [x, y, width, height] = prediction.bbox;
                    const className = prediction.class;
                    const score = prediction.score;
                    
                    // 设置边框样式
                    ctx.strokeStyle = '#4CAF50';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(x, y, width, height);
                    
                    // 设置文本背景
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    const textWidth = ctx.measureText(`${className}: ${Math.round(score * 100)}%`).width;
                    ctx.fillRect(x, y - 30, textWidth + 20, 30);
                    
                    // 设置文本样式
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText(`${className}: ${Math.round(score * 100)}%`, x + 10, y - 10);
                    
                    // 更新检测到的物体列表
                    if (!detectedObjects[className]) {
                        detectedObjects[className] = {
                            time: currentTime,
                            formatted: formatDateTime(currentTime)
                        };
                        
                        // 添加到表格
                        updateDetectionTable();
                    }
                });
            } catch (error) {
                console.error('目标检测错误:', error);
            }
        }

        // 更新检测表格
        function updateDetectionTable() {
            detectionTableBody.innerHTML = '';
            
            // 按首次检测时间排序
            const sortedObjects = Object.entries(detectedObjects).sort((a, b) => a[1].time - b[1].time);
            
            for (const [className, data] of sortedObjects) {
                const row = document.createElement('tr');
                
                const classCell = document.createElement('td');
                classCell.textContent = className;
                row.appendChild(classCell);
                
                const timeCell = document.createElement('td');
                timeCell.textContent = data.formatted;
                row.appendChild(timeCell);
                
                detectionTableBody.appendChild(row);
            }
        }

        // 格式化日期时间
        function formatDateTime(date) {
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${hours}:${minutes}:${seconds}`;
        }

        // 停止检测
        function stopDetection() {
            isRunning = false;
            clearInterval(detectionInterval);
            
            // 停止所有视频轨道
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            
            // 更新按钮状态
            startButton.textContent = '开始桌面检测';
            startButton.disabled = false;
            
            // 更新状态
            statusElement.className = 'status success';
            statusElement.textContent = '检测已停止。点击"开始桌面检测"按钮重新开始。';
        }

        // 窗口大小变化时调整画布大小
        window.addEventListener('resize', () => {
            if (video.videoWidth) {
                const videoAspectRatio = video.videoWidth / video.videoHeight;
                const maxWidth = Math.min(video.videoWidth, video.parentElement.clientWidth);
                const maxHeight = maxWidth / videoAspectRatio;
                
                // 更新视频和画布的显示尺寸
                video.style.width = `${maxWidth}px`;
                video.style.height = `${maxHeight}px`;
                canvas.style.width = `${maxWidth}px`;
                canvas.style.height = `${maxHeight}px`;
            }
        });
    </script>
</body>
</html> 