#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
from typing import Tuple, Optional
import json
from datetime import datetime

class GomokuGame:
    def __init__(self):
        self.board_size = 15
        self.board = [[' ' for _ in range(self.board_size)] for _ in range(self.board_size)]
        self.current_player = '●'  # 黑子先手
        self.last_move: Optional[Tuple[int, int]] = None
        self.game_over = False
        self.start_time = time.time()
        self.move_count = 0

    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')

    def print_board(self):
        self.clear_screen()
        print('\n   ' + ' '.join(chr(65 + i) for i in range(self.board_size)))
        for i in range(self.board_size):
            row = f"{i+1:2d} "
            for j in range(self.board_size):
                if self.board[i][j] == ' ':
                    row += '\x1b[90m┼\x1b[0m '
                else:
                    color = '\x1b[31m' if self.board[i][j] == '●' else '\x1b[34m'
                    row += f"{color}{self.board[i][j]}\x1b[0m "
            print(row)

    def is_valid_move(self, row: int, col: int) -> bool:
        if not (0 <= row < self.board_size and 0 <= col < self.board_size):
            return False
        return self.board[row][col] == ' '

    def make_move(self, row: int, col: int) -> bool:
        if not self.is_valid_move(row, col):
            return False
        
        self.board[row][col] = self.current_player
        self.last_move = (row, col)
        self.move_count += 1
        
        if self.check_win(row, col):
            self.game_over = True
            return True
            
        self.current_player = '○' if self.current_player == '●' else '●'
        return True

    def check_win(self, row: int, col: int) -> bool:
        directions = [(1,0), (0,1), (1,1), (1,-1)]
        for dr, dc in directions:
            count = 1
            # 正向检查
            for i in range(1, 5):
                r, c = row + dr*i, col + dc*i
                if not (0 <= r < self.board_size and 0 <= c < self.board_size):
                    break
                if self.board[r][c] != self.current_player:
                    break
                count += 1
            # 反向检查
            for i in range(1, 5):
                r, c = row - dr*i, col - dc*i
                if not (0 <= r < self.board_size and 0 <= c < self.board_size):
                    break
                if self.board[r][c] != self.current_player:
                    break
                count += 1
            if count >= 5:
                return True
        return False

    def save_game(self):
        game_state = {
            'board': self.board,
            'current_player': self.current_player,
            'last_move': self.last_move,
            'move_count': self.move_count,
            'timestamp': datetime.now().isoformat()
        }
        with open('gomoku_save.json', 'w', encoding='utf-8') as f:
            json.dump(game_state, f, ensure_ascii=False, indent=2)

    def load_game(self):
        try:
            with open('gomoku_save.json', 'r', encoding='utf-8') as f:
                game_state = json.load(f)
                self.board = game_state['board']
                self.current_player = game_state['current_player']
                self.last_move = tuple(game_state['last_move']) if game_state['last_move'] else None
                self.move_count = game_state['move_count']
        except FileNotFoundError:
            print("没有找到存档文件")

def main():
    game = GomokuGame()
    
    while not game.game_over:
        game.print_board()
        print(f"\n当前回合：\x1b[{'31' if game.current_player == '●' else '34'}m{game.current_player}\x1b[0m")
        print(f"已用时间：{int(time.time() - game.start_time)}秒")
        
        try:
            move = input("请输入坐标（例如：A1）或输入'save'保存游戏：").strip().upper()
            if move.lower() == 'save':
                game.save_game()
                print("游戏已保存")
                continue
                
            if len(move) < 2:
                print("无效的输入！")
                continue
                
            col = ord(move[0]) - ord('A')
            row = int(move[1:]) - 1
            
            if not game.make_move(row, col):
                print("无效的移动！")
                time.sleep(1)
                
        except (ValueError, IndexError):
            print("无效的输入！")
            time.sleep(1)
    
    game.print_board()
    winner = '黑方' if game.current_player == '●' else '白方'
    print(f"\n游戏结束！{winner}获胜！")

if __name__ == "__main__":
    main() 