* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.container {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 1.5rem;
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.player-info {
    display: flex;
    gap: 2rem;
}

.player {
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: bold;
}

.player.black {
    background-color: #333;
    color: white;
}

.player.white {
    background-color: #fff;
    color: #333;
    border: 1px solid #333;
}

.timer {
    font-size: 1.2rem;
    color: #666;
}

.board-container {
    margin: 1rem 0;
}

#board {
    background-color: #DEB887;
    border: 2px solid #8B4513;
}

.controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1rem 0;
}

button {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#newGame {
    background-color: #4CAF50;
    color: white;
}

#saveGame {
    background-color: #2196F3;
    color: white;
}

#loadGame {
    background-color: #FF9800;
    color: white;
}

button:hover {
    opacity: 0.9;
}

.message {
    text-align: center;
    margin-top: 1rem;
    min-height: 1.5rem;
    color: #666;
} 