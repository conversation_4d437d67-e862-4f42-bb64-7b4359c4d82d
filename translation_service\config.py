"""
配置文件 - API密钥和其他配置项
"""

# API密钥配置
API_KEYS = {
    "tencent": {
        "secret_id": "AKIDlZL1e9WlrkOnxsY3hGB9wY4jKl3A7ubB",  # 腾讯云SecretId
        "secret_key": "SuAB2jU7ax87vZ3PYaSLh3WnvxpfSAKP"  # 腾讯云SecretKey
    },
    "openai": {
        "api_key": ""  # OpenAI API密钥
    },
    "azure": {
        "key": "",  # Azure翻译服务密钥
        "region": ""  # Azure服务区域
    },
    "baidu": {
        "app_id": "",  # 百度翻译应用ID
        "app_key": ""  # 百度翻译应用密钥
    }
}

# 默认使用的翻译服务提供商
DEFAULT_PROVIDER = "tencent"

# Whisper模型配置
WHISPER_MODEL = "base"  # 可选: tiny, base, small, medium, large

# 音频配置
AUDIO_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,
    "chunk_size": 1024,
    "default_duration": 5  # 默认录音时长（秒）
} 