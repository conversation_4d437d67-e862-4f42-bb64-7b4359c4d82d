from openai import OpenAI
from alibabacloud_tea_openapi.models import Config
from alibabacloud_searchplat20240529.client import Client
from alibabacloud_searchplat20240529.models import GetWebSearchRequest

# Set the API key and base URL
client = OpenAI(base_url="https://api.deepseek.com", api_key="***********************************")
messages = []

# 初始化阿里云搜索客户端
search_config = Config(
    bearer_token="OS-16p5o2w9igj802h2",
    endpoint="default-o0gc.platform-cn-shanghai.opensearch.aliyuncs.com",
    protocol="http"
)
search_client = Client(search_config)

print("欢迎使用对话助手！输入'退出'结束对话。")

while True:
    user_input = input("你: ")
    if user_input.lower() in ["退出", "exit"]:
        print("对话结束，再见！")
        break
        
    messages.append({"role": "user", "content": user_input})
    
    # 使用阿里云SDK调用搜索API
    try:
        search_results = []
        # 创建搜索请求
        request = GetWebSearchRequest(query=user_input)
        # 发送API请求
        response = search_client.get_web_search("default", "ops-web-search-001", request)
        
        # 解析搜索结果
        if response.body and hasattr(response.body, 'result'):
            result_obj = response.body.result
            
            # 解析搜索结果
            if hasattr(result_obj, 'search_result') and result_obj.search_result:
                search_data = result_obj.search_result
                
                # 检查search_data是否是列表
                if isinstance(search_data, list) and len(search_data) > 0:
                    # 获取前2条结果
                    for i in range(min(2, len(search_data))):
                        item = search_data[i]
                        # 使用正确的属性名：tilte (注意不是title) 和 content/snippet
                        title = getattr(item, 'tilte', '无标题')  # 注意是tilte不是title
                        content = getattr(item, 'content', getattr(item, 'snippet', '无摘要'))
                        
                        
                        search_results.append(f"{title}: {content}")
                else:
                    search_results = ["没有找到搜索结果"]
                

            else:
                search_results = ["搜索结果解析失败"]
            
    except Exception as e:
        print(f"搜索API调用失败: {str(e)}")
        search_results = ["搜索服务暂时不可用"]
    
    # 将搜索结果作为system上下文添加
    if search_results:
        search_context = f"实时搜索结果（百度）:\n{chr(10).join(search_results)}"
        messages.append({
            "role": "system",
            "content": search_context
        })
    
    # 调用DeepSeek API
    response = client.chat.completions.create(
        model="deepseek-reasoner",
        messages=messages
    )
    
    assistant_reply = response.choices[0].message.content
    print(f"助手: {assistant_reply}")
    
    # 将助手回复加入对话历史
    messages.append({"role": "assistant", "content": assistant_reply}) 