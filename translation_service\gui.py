"""
用户界面模块
"""
import sys
import os
import tempfile
from PyQt5.QtWidgets import (
    QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QTextEdit, 
    QWidget, QComboBox, QLabel, QSpinBox, QStatusBar, QAction, 
    QFileDialog, QMessageBox, QGroupBox, QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QTextCursor
import logging

from audio_processor import AudioProcessor
from translation_service import TranslationService, ApiProvider

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RecordingThread(QThread):
    """录音线程，避免UI冻结"""
    recording_finished = pyqtSignal(str)
    audio_array_finished = pyqtSignal(object)  # 新增：用于传递音频数组
    error_occurred = pyqtSignal(str)
    
    def __init__(self, audio_processor, duration=5, use_array=False):
        super().__init__()
        self.audio_processor = audio_processor
        self.duration = duration
        self.use_array = use_array  # 新增：是否使用数组模式
        
    def run(self):
        try:
            if self.use_array:
                # 使用数组模式录音
                audio_array = self.audio_processor.record_audio_to_array(self.duration)
                self.audio_array_finished.emit(audio_array)
            else:
                # 使用文件模式录音
                temp_filename = self.audio_processor.record_audio(self.duration)
                self.recording_finished.emit(temp_filename)
        except Exception as e:
            logger.error(f"录音过程出错: {str(e)}")
            self.error_occurred.emit(str(e))

class TranscriptionThread(QThread):
    """转录线程，避免UI冻结"""
    transcription_finished = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, audio_processor, audio_file):
        super().__init__()
        self.audio_processor = audio_processor
        self.audio_file = audio_file
        
    def run(self):
        try:
            transcribed_text = self.audio_processor.transcribe(self.audio_file)
            self.transcription_finished.emit(transcribed_text)
        except Exception as e:
            logger.error(f"转录过程出错: {str(e)}")
            self.error_occurred.emit(str(e))

class TranslationThread(QThread):
    """翻译线程，避免UI冻结"""
    translation_finished = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, translation_service, text, source_lang, target_lang):
        super().__init__()
        self.translation_service = translation_service
        self.text = text
        self.source_lang = source_lang
        self.target_lang = target_lang
        
    def run(self):
        try:
            translated_text = self.translation_service.translate(
                self.text, self.source_lang, self.target_lang
            )
            self.translation_finished.emit(translated_text)
        except Exception as e:
            logger.error(f"翻译过程出错: {str(e)}")
            self.error_occurred.emit(str(e))

class TranslationApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.audio_processor = None
        self.translation_service = None
        self.temp_audio_file = None
        self.recording_thread = None
        self.transcription_thread = None
        self.translation_thread = None
        self.setup_audio_processor()
        self.setup_translation_service()
        self.init_ui()
        
    def setup_audio_processor(self, model_size="base"):
        """设置音频处理器"""
        try:
            self.audio_processor = AudioProcessor(model_size)
            logger.info("音频处理器初始化成功")
        except Exception as e:
            logger.error(f"音频处理器初始化失败: {str(e)}")
            self.audio_processor = None
            # 只在有GUI的情况下显示错误对话框
            try:
                QMessageBox.warning(self, "警告", f"音频处理器初始化失败: {str(e)}\n\n程序将继续运行，但录音功能可能受限。")
            except:
                # 如果GUI还没有准备好，只记录错误
                logger.warning("GUI未准备好，跳过错误对话框显示")
            
    def setup_translation_service(self, provider=ApiProvider.TENCENT, api_key=None):
        """设置翻译服务"""
        try:
            self.translation_service = TranslationService(provider, api_key)
            logger.info(f"翻译服务初始化成功，提供商: {provider.value}")
        except Exception as e:
            logger.error(f"翻译服务初始化失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"翻译服务初始化失败: {str(e)}")
            
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("实时翻译工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 控制面板
        control_panel = self.create_control_panel()
        main_layout.addLayout(control_panel)
        
        # 文本区域
        text_panel = self.create_text_panel()
        main_layout.addLayout(text_panel)
        
        # 设置菜单栏
        self.create_menu_bar()
        
        # 状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪")
        
        # 设置中心部件
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
    def create_control_panel(self):
        """创建控制面板"""
        control_layout = QHBoxLayout()
        
        # 录音控制
        record_group = QGroupBox("录音控制")
        record_layout = QVBoxLayout()
        
        record_controls = QHBoxLayout()
        self.record_button = QPushButton("开始录音")
        self.record_button.clicked.connect(self.start_recording)
        record_controls.addWidget(self.record_button)
        
        self.stop_button = QPushButton("停止录音")
        self.stop_button.clicked.connect(self.stop_recording)
        self.stop_button.setEnabled(False)
        record_controls.addWidget(self.stop_button)
        
        record_layout.addLayout(record_controls)
        
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("录音时长(秒):"))
        self.duration_spinner = QSpinBox()
        self.duration_spinner.setRange(1, 60)
        self.duration_spinner.setValue(5)
        duration_layout.addWidget(self.duration_spinner)
        
        record_layout.addLayout(duration_layout)
        record_group.setLayout(record_layout)
        control_layout.addWidget(record_group)
        
        # 翻译控制
        translation_group = QGroupBox("翻译控制")
        translation_layout = QVBoxLayout()
        
        # 源语言和目标语言
        lang_layout = QHBoxLayout()
        
        source_layout = QVBoxLayout()
        source_layout.addWidget(QLabel("源语言:"))
        self.source_lang_combo = QComboBox()
        self.source_lang_combo.addItem("自动检测", "auto")
        self.source_lang_combo.addItem("中文", "zh")
        self.source_lang_combo.addItem("英语", "en")
        self.source_lang_combo.addItem("日语", "ja")
        self.source_lang_combo.addItem("韩语", "ko")
        self.source_lang_combo.addItem("法语", "fr")
        self.source_lang_combo.addItem("西班牙语", "es")
        source_layout.addWidget(self.source_lang_combo)
        lang_layout.addLayout(source_layout)
        
        target_layout = QVBoxLayout()
        target_layout.addWidget(QLabel("目标语言:"))
        self.target_lang_combo = QComboBox()
        self.target_lang_combo.addItem("中文", "zh")
        self.target_lang_combo.addItem("英语", "en")
        self.target_lang_combo.addItem("日语", "ja")
        self.target_lang_combo.addItem("韩语", "ko")
        self.target_lang_combo.addItem("法语", "fr")
        self.target_lang_combo.addItem("西班牙语", "es")
        target_layout.addWidget(self.target_lang_combo)
        lang_layout.addLayout(target_layout)
        
        translation_layout.addLayout(lang_layout)
        
        # API提供商选择
        provider_layout = QHBoxLayout()
        provider_layout.addWidget(QLabel("API提供商:"))
        self.provider_combo = QComboBox()
        self.provider_combo.addItem("腾讯云", ApiProvider.TENCENT.value)
        self.provider_combo.addItem("OpenAI", ApiProvider.OPENAI.value)
        self.provider_combo.addItem("Azure", ApiProvider.AZURE.value)
        self.provider_combo.addItem("百度", ApiProvider.BAIDU.value)
        self.provider_combo.setCurrentIndex(0)  # 默认选择腾讯云
        self.provider_combo.currentIndexChanged.connect(self.change_api_provider)
        provider_layout.addWidget(self.provider_combo)
        
        translation_layout.addLayout(provider_layout)
        
        # 翻译按钮
        self.translate_button = QPushButton("翻译")
        self.translate_button.clicked.connect(self.translate_text)
        translation_layout.addWidget(self.translate_button)
        
        translation_group.setLayout(translation_layout)
        control_layout.addWidget(translation_group)
        
        return control_layout
        
    def create_text_panel(self):
        """创建文本显示面板"""
        text_layout = QHBoxLayout()
        
        # 原文
        source_group = QGroupBox("原文")
        source_layout = QVBoxLayout()
        self.source_text = QTextEdit()
        self.source_text.setAcceptRichText(False)
        source_layout.addWidget(self.source_text)
        
        source_controls = QHBoxLayout()
        self.clear_source_button = QPushButton("清空")
        self.clear_source_button.clicked.connect(lambda: self.source_text.clear())
        source_controls.addWidget(self.clear_source_button)
        
        self.transcribe_button = QPushButton("识别")
        self.transcribe_button.clicked.connect(self.transcribe_audio)
        source_controls.addWidget(self.transcribe_button)
        
        source_layout.addLayout(source_controls)
        source_group.setLayout(source_layout)
        text_layout.addWidget(source_group)
        
        # 翻译结果
        target_group = QGroupBox("翻译结果")
        target_layout = QVBoxLayout()
        self.target_text = QTextEdit()
        self.target_text.setReadOnly(True)
        target_layout.addWidget(self.target_text)
        
        target_controls = QHBoxLayout()
        self.clear_target_button = QPushButton("清空")
        self.clear_target_button.clicked.connect(lambda: self.target_text.clear())
        target_controls.addWidget(self.clear_target_button)
        
        self.copy_target_button = QPushButton("复制")
        self.copy_target_button.clicked.connect(self.copy_result)
        target_controls.addWidget(self.copy_target_button)
        
        self.save_result_button = QPushButton("保存")
        self.save_result_button.clicked.connect(self.save_result)
        target_controls.addWidget(self.save_result_button)
        
        target_layout.addLayout(target_controls)
        target_group.setLayout(target_layout)
        text_layout.addWidget(target_group)
        
        return text_layout
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()
        
        # 文件菜单
        file_menu = menu_bar.addMenu("文件")
        
        open_action = QAction("打开音频文件", self)
        open_action.triggered.connect(self.open_audio_file)
        file_menu.addAction(open_action)
        
        save_action = QAction("保存翻译结果", self)
        save_action.triggered.connect(self.save_result)
        file_menu.addAction(save_action)
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = menu_bar.addMenu("设置")
        
        api_action = QAction("API设置", self)
        api_action.triggered.connect(self.show_api_settings)
        settings_menu.addAction(api_action)
        
        whisper_action = QAction("Whisper模型设置", self)
        whisper_action.triggered.connect(self.show_whisper_settings)
        settings_menu.addAction(whisper_action)
        
        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def start_recording(self):
        """开始录音"""
        try:
            # 检查音频处理器是否可用
            if not self.audio_processor:
                QMessageBox.warning(self, "警告", "音频处理器未初始化，无法录音。\n\n请检查音频设备和权限设置。")
                return
                
            duration = self.duration_spinner.value()
            self.statusBar.showMessage(f"正在录音，时长{duration}秒...")
            
            # 禁用录音按钮，启用停止按钮
            self.record_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            
            # 启动录音线程
            self.recording_thread = RecordingThread(self.audio_processor, duration)
            self.recording_thread.recording_finished.connect(self.on_recording_finished)
            self.recording_thread.audio_array_finished.connect(self.on_audio_array_finished)
            self.recording_thread.error_occurred.connect(self.on_recording_error)
            self.recording_thread.start()
            
        except Exception as e:
            logger.error(f"开始录音失败: {str(e)}")
            self.statusBar.showMessage(f"开始录音失败: {str(e)}")
            self.record_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            
    def stop_recording(self):
        """停止录音"""
        if self.recording_thread and self.recording_thread.isRunning():
            # 这里我们无法直接停止PyAudio录音，但可以尝试做一些清理
            self.statusBar.showMessage("尝试停止录音...")
            
        # 重置UI状态
        self.record_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
    def on_recording_finished(self, temp_filename):
        """录音完成回调"""
        self.temp_audio_file = temp_filename
        self.statusBar.showMessage(f"录音完成，保存至: {temp_filename}")
        
        # 恢复UI状态
        self.record_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 自动开始转录
        self.transcribe_audio()
        
    def on_audio_array_finished(self, audio_array):
        """数组录音完成回调"""
        self.temp_audio_file = audio_array
        self.statusBar.showMessage("录音完成，保存至: 数组")
        
        # 恢复UI状态
        self.record_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 自动开始转录
        self.transcribe_audio()
        
    def on_recording_error(self, error_msg):
        """录音错误回调"""
        self.statusBar.showMessage(f"录音错误: {error_msg}")
        
        # 恢复UI状态
        self.record_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
    def transcribe_audio(self):
        """转录音频"""
        if not self.temp_audio_file:
            self.statusBar.showMessage("没有可用的音频文件")
            return
            
        # 检查音频处理器是否可用
        if not self.audio_processor:
            QMessageBox.warning(self, "警告", "音频处理器未初始化，无法进行语音识别。")
            return
            
        try:
            self.statusBar.showMessage("正在转录音频...")
            
            # 启动转录线程
            self.transcription_thread = TranscriptionThread(self.audio_processor, self.temp_audio_file)
            self.transcription_thread.transcription_finished.connect(self.on_transcription_finished)
            self.transcription_thread.error_occurred.connect(self.on_transcription_error)
            self.transcription_thread.start()
            
        except Exception as e:
            logger.error(f"开始转录失败: {str(e)}")
            self.statusBar.showMessage(f"开始转录失败: {str(e)}")
            
    def on_transcription_finished(self, transcribed_text):
        """转录完成回调"""
        self.source_text.setText(transcribed_text)
        self.statusBar.showMessage("转录完成")
        
        # 自动开始翻译
        self.translate_text()
        
    def on_transcription_error(self, error_msg):
        """转录错误回调"""
        self.statusBar.showMessage(f"转录错误: {error_msg}")
        
    def translate_text(self):
        """翻译文本"""
        text = self.source_text.toPlainText().strip()
        if not text:
            self.statusBar.showMessage("没有可翻译的文本")
            return
            
        source_lang = self.source_lang_combo.currentData()
        target_lang = self.target_lang_combo.currentData()
        
        try:
            self.statusBar.showMessage(f"正在翻译，从{source_lang}到{target_lang}...")
            
            # 启动翻译线程
            self.translation_thread = TranslationThread(
                self.translation_service, text, source_lang, target_lang
            )
            self.translation_thread.translation_finished.connect(self.on_translation_finished)
            self.translation_thread.error_occurred.connect(self.on_translation_error)
            self.translation_thread.start()
            
        except Exception as e:
            logger.error(f"开始翻译失败: {str(e)}")
            self.statusBar.showMessage(f"开始翻译失败: {str(e)}")
            
    def on_translation_finished(self, translated_text):
        """翻译完成回调"""
        self.target_text.setText(translated_text)
        self.statusBar.showMessage("翻译完成")
        
    def on_translation_error(self, error_msg):
        """翻译错误回调"""
        self.statusBar.showMessage(f"翻译错误: {error_msg}")
        
    def open_audio_file(self):
        """打开音频文件"""
        file_dialog = QFileDialog()
        audio_file, _ = file_dialog.getOpenFileName(
            self, "打开音频文件", "", "音频文件 (*.mp3 *.wav *.m4a *.mp4)"
        )
        
        if audio_file:
            self.temp_audio_file = audio_file
            self.statusBar.showMessage(f"已加载音频文件: {audio_file}")
            
            # 自动开始转录
            self.transcribe_audio()
            
    def save_result(self):
        """保存翻译结果"""
        text = self.target_text.toPlainText()
        if not text:
            self.statusBar.showMessage("没有可保存的翻译结果")
            return
            
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self, "保存翻译结果", "", "文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                self.statusBar.showMessage(f"翻译结果已保存至: {file_path}")
            except Exception as e:
                logger.error(f"保存文件失败: {str(e)}")
                self.statusBar.showMessage(f"保存文件失败: {str(e)}")
                
    def copy_result(self):
        """复制翻译结果到剪贴板"""
        text = self.target_text.toPlainText()
        if text:
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            self.statusBar.showMessage("翻译结果已复制到剪贴板")
            
    def change_api_provider(self):
        """更改API提供商"""
        provider_value = self.provider_combo.currentData()
        
        # 转换为枚举
        for provider in ApiProvider:
            if provider.value == provider_value:
                self.setup_translation_service(provider)
                break
                
    def show_api_settings(self):
        """显示API设置对话框"""
        # 实际项目中可以实现API设置对话框
        QMessageBox.information(self, "API设置", "请在环境变量中设置相应的API密钥，或者通过配置文件设置。")
        
    def show_whisper_settings(self):
        """显示Whisper模型设置对话框"""
        # 实际项目中可以实现Whisper设置对话框
        model_sizes = ["tiny", "base", "small", "medium", "large"]
        current_model = "base"  # 默认模型
        
        msg = QMessageBox()
        msg.setWindowTitle("Whisper模型设置")
        msg.setText("选择Whisper模型大小:\n\n" +
                   "注意：模型越大，识别越准确，但资源消耗也越多。\n" +
                   "- tiny: 最小模型，资源消耗低，精度较低\n" +
                   "- base: 平衡模型，资源消耗适中，精度适中\n" +
                   "- small: 中等模型，资源消耗较高，精度较高\n" +
                   "- medium: 较大模型，资源消耗高，精度高\n" +
                   "- large: 最大模型，资源消耗很高，精度最高")
        
        for model in model_sizes:
            button = msg.addButton(model, QMessageBox.ActionRole)
            if model == current_model:
                msg.setDefaultButton(button)
                
        msg.addButton("取消", QMessageBox.RejectRole)
        
        msg.exec_()
        
        clicked_button = msg.clickedButton()
        if clicked_button and clicked_button.text() in model_sizes:
            selected_model = clicked_button.text()
            self.setup_audio_processor(selected_model)
            self.statusBar.showMessage(f"已切换到Whisper {selected_model}模型")
            
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, 
            "关于实时翻译工具",
            "实时翻译工具 v1.0\n\n"
            "基于Whisper进行本地语音识别\n"
            "支持多种翻译API\n\n"
            "© 2023 版权所有"
        )
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理临时文件
        if self.temp_audio_file and os.path.exists(self.temp_audio_file):
            try:
                os.unlink(self.temp_audio_file)
                logger.info(f"临时文件已删除: {self.temp_audio_file}")
            except:
                pass
                
        # 关闭所有线程
        if self.recording_thread and self.recording_thread.isRunning():
            self.recording_thread.terminate()
            
        if self.transcription_thread and self.transcription_thread.isRunning():
            self.transcription_thread.terminate()
            
        if self.translation_thread and self.translation_thread.isRunning():
            self.translation_thread.terminate()
            
        event.accept() 