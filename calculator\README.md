# 专业计算器

这是一个基于Web的专业计算器应用，提供基本和科学计算功能。

## 功能

- 基本运算：加、减、乘、除
- 科学计算：平方根、平方、三角函数（正弦、余弦、正切）、对数计算（log、ln）
- 特殊常数：π (Pi)、e（自然对数的底数）
- 历史记录显示
- 键盘支持

## 技术栈

- HTML5
- CSS3
- JavaScript (ES6+)

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 使用鼠标点击按钮进行计算
3. 也可以使用键盘输入：
   - 数字键：0-9
   - 运算符：+, -, *, /
   - 小数点：.
   - 括号：(, )
   - 计算结果：Enter 或 =
   - 退格：Backspace
   - 清除所有：Esc

## 界面预览

计算器提供了现代化的用户界面，包含明显的显示区域和易于点击的按钮。界面采用了深色主题，视觉效果舒适。

## 注意事项

- 对于科学计算，必须先输入数值，再点击对应的函数按钮
- 在计算三角函数时，输入的角度值是以弧度为单位
- 对数和平方根函数不支持负数作为输入 