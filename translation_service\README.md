# Windows实时翻译工具

## 项目简介
这是一个基于Windows的实时翻译工具，使用Whisper进行本地语音识别，并通过多种LLM API实现翻译功能。应用支持录音、识别、翻译和文本导出等功能。

## 主要功能
- 实时录音和语音识别
- 支持多种语言翻译
- 多种翻译API选择（OpenAI, 腾讯云, Azure, 百度）
- 音频文件导入和翻译
- 翻译结果保存

## 安装说明

### 环境要求
- Python 3.8+
- Windows 10/11

### 安装步骤
1. 克隆或下载本仓库

2. 安装依赖项（推荐方法）：

   **方法一：使用安装脚本（推荐）**
   ```bash
   # Windows用户直接双击运行
   install_dependencies.bat
   ```

   **方法二：手动安装**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 先安装pipwin来获取PyAudio预编译包
   pip install pipwin
   pipwin install pyaudio
   
   # 然后安装其他依赖
   pip install -r requirements.txt
   ```

   **方法三：如果上述方法失败**
   ```bash
   # 尝试使用conda安装PyAudio
   conda install pyaudio
   pip install -r requirements.txt
   ```
3. 设置API密钥环境变量（根据您选择的翻译服务）
```bash
# OpenAI
set OPENAI_API_KEY=your_api_key

# 腾讯云
set TENCENT_API_KEY=your_secret_id:your_secret_key

# Azure
set AZURE_API_KEY=your_api_key:your_region

# 百度
set BAIDU_API_KEY=your_app_id:your_app_key
```

## 使用方法
运行以下命令启动应用：
```bash
python main.py
```

### 使用步骤
1. 选择源语言和目标语言
2. 点击"开始录音"按钮进行录音
3. 录音完成后，应用会自动进行语音识别和翻译
4. 也可以手动输入文本并点击"翻译"按钮
5. 翻译结果可以复制或保存到文件

### 其他功能
- 点击"文件"->"打开音频文件"可以打开本地音频文件进行识别和翻译
- 在"设置"->"Whisper模型设置"中可以选择不同大小的Whisper模型
- 在"设置"->"API设置"中可以配置翻译API

## 技术架构
- Whisper：用于本地语音识别
- PyAudio：用于音频录制
- OpenAI/腾讯云/Azure/百度API：用于文本翻译
- PyQt5：用于构建图形用户界面

## 注意事项
- **Python版本兼容性**：本项目支持Python 3.8-3.11，如果使用Python 3.12+，请使用提供的安装脚本或按照README中的特殊安装说明
- Whisper模型会占用一定的系统资源，特别是较大的模型
- 首次使用时，应用会下载所需的Whisper模型，请确保有稳定的网络连接
- 翻译API可能有使用频率和次数限制，请参考各API提供商的文档
- 如果遇到PyAudio安装问题，请尝试使用预编译的wheel包或conda安装

## 许可证
MIT

## 联系方式
如有问题或建议，请提交Issue或联系作者。 