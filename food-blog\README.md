# 美食博客网站

这是一个使用现代网页技术构建的美食博客网站。

## 项目特点

- 响应式设计，适配移动端和桌面端
- 现代CSS特性（Grid、Flexbox、CSS变量）
- 语义化HTML结构
- 无障碍设计支持
- 性能优化（图片懒加载、代码压缩）

## 项目结构

```
food-blog/
├── index.html          # 首页
├── css/
│   └── style.css       # 主样式文件
├── js/
│   └── main.js         # 主脚本文件
└── images/             # 图片资源目录
```

## 技术栈

- HTML5
- CSS3
- 原生JavaScript

## 浏览器支持

- Chrome (最新版)
- Firefox (最新版)
- Safari (最新版)
- Edge (最新版)

## 开发指南

1. 克隆项目到本地
2. 使用现代浏览器打开 `index.html` 文件
3. 修改 `css/style.css` 文件自定义样式
4. 在 `images` 目录中添加图片资源

## 性能优化

- 使用 `loading="lazy"` 实现图片懒加载
- 使用CSS Grid和Flexbox实现响应式布局
- 优化图片大小，单张图片不超过200KB
- 使用CSS变量实现主题定制

## 无障碍支持

- 符合WCAG 2.1标准
- 支持键盘导航
- 提供适当的ARIA标签
- 支持屏幕阅读器

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License 