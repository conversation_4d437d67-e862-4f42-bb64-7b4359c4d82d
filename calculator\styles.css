* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(45deg, #2c3e50, #3498db);
}

.calculator {
    width: 350px;
    background-color: #1e272e;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    padding: 20px;
}

.display {
    width: 100%;
    background-color: #384d5b;
    border-radius: 10px;
    margin-bottom: 20px;
    padding: 15px;
    color: white;
}

.history {
    height: 25px;
    color: #ccc;
    text-align: right;
    font-size: 14px;
    margin-bottom: 5px;
    overflow: hidden;
    white-space: nowrap;
}

.result {
    width: 100%;
    height: 45px;
    border: none;
    outline: none;
    background: transparent;
    color: white;
    font-size: 24px;
    text-align: right;
}

.row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

button {
    width: 70px;
    height: 55px;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s;
}

button:active {
    transform: scale(0.95);
}

.number-btn {
    background-color: #485460;
    color: white;
}

.number-btn:hover {
    background-color: #5c6877;
}

.operation-btn {
    background-color: #ff9f43;
    color: white;
    font-weight: bold;
}

.operation-btn:hover {
    background-color: #ffa953;
}

.function-btn {
    background-color: #576574;
    color: white;
}

.function-btn:hover {
    background-color: #6a7885;
}

.equal-btn {
    background-color: #2e86de;
    color: white;
    font-weight: bold;
}

.equal-btn:hover {
    background-color: #54a0ff;
}

.scientific-row {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.scientific-row button {
    width: calc(100% / 6 - 6px);
    height: 45px;
    font-size: 14px;
}

.science-btn {
    background-color: #303952;
    color: #dfe4ea;
}

.science-btn:hover {
    background-color: #3a4463;
} 