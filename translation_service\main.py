"""
主程序入口
"""
import sys
import logging
from PyQt5.QtWidgets import QApplication, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation_app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

from gui import TranslationApp

def check_dependencies():
    """检查依赖项是否安装"""
    try:
        import openai
        import pyaudio
        import whisper
        import numpy
        import PyQt5
        logger.info("所有依赖已安装")
        return True
    except ImportError as e:
        logger.error(f"缺少依赖: {str(e)}")
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(
            None, 
            "依赖错误", 
            f"缺少必要的依赖项: {str(e)}\n\n"
            "请运行以下命令安装依赖:\n"
            "pip install pyaudio whisper openai PyQt5 numpy"
        )
        return False

def main():
    """主程序入口"""
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("实时翻译工具")
    
    # 检查依赖项
    if not check_dependencies():
        return 1
        
    # 显示启动画面
    splash_pix = QPixmap("splash.png") if os.path.exists("splash.png") else QPixmap(400, 300)
    if splash_pix.isNull():
        # 创建空白启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
    splash = QSplashScreen(splash_pix)
    splash.showMessage("正在加载Whisper模型...", Qt.AlignBottom | Qt.AlignCenter, Qt.black)
    splash.show()
    app.processEvents()
    
    # 创建主窗口
    try:
        window = TranslationApp()
        
        # 延迟关闭启动画面，显示主窗口
        def show_main_window():
            window.show()
            splash.finish(window)
            
        QTimer.singleShot(2000, show_main_window)
        
        return app.exec_()
        
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(None, "错误", f"应用启动失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 