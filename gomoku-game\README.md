# 五子棋游戏

这是一个基于Python开发的五子棋游戏，支持双人对战模式。

## 功能特点

- 15x15标准棋盘
- 黑白双方轮流落子
- 自动判断胜负
- 支持游戏存档和读档
- 彩色终端显示
- 实时显示游戏时间

## 系统要求

- Python 3.6+
- 支持ANSI颜色的终端

## 安装方法

1. 确保您的系统已安装Python 3.6或更高版本
2. 克隆或下载本项目
3. 进入项目目录

## 运行游戏

```bash
python gomoku.py
```

## 游戏规则

1. 黑方先手，双方轮流落子
2. 在空位置落子
3. 任意一方在横向、纵向或斜向连成五子即获胜
4. 输入格式为"字母+数字"，例如：A1、B2等
5. 输入"save"可以保存当前游戏

## 操作说明

- 输入坐标：输入字母（A-O）和数字（1-15）的组合
- 保存游戏：输入"save"
- 退出游戏：按Ctrl+C

## 注意事项

- 确保终端支持ANSI颜色显示
- 游戏存档文件名为"gomoku_save.json"
- 坐标输入不区分大小写

## 开发者

本游戏由AI助手开发，遵循MIT开源协议。 