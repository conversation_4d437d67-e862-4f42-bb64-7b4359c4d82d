"""
音频采集和处理模块
"""
import numpy as np
import whisper
import wave
import tempfile
import os
import logging
import sounddevice as sd
import threading
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessor:
    def __init__(self, model_size="base"):
        logger.info(f"初始化AudioProcessor，加载Whisper模型: {model_size}")
        self.model = whisper.load_model(model_size)
        
        # 音频参数
        self.channels = 1
        self.rate = 16000
        self.chunk = 1024
        
        # 录音状态
        self.is_recording = False
        self.recording_data = []
        self.recording_thread = None
        
        # 设置项目临时文件夹
        self.temp_dir = self._setup_temp_directory()
        
        # 检查sounddevice设备
        self._check_audio_devices()
        
    def _setup_temp_directory(self):
        """设置项目临时文件夹"""
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir = os.path.join(current_dir, 'temp')
        
        # 创建temp目录（如果不存在）
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
            logger.info(f"创建临时文件夹: {temp_dir}")
        else:
            logger.info(f"使用临时文件夹: {temp_dir}")
            
        return temp_dir
        
    def _generate_temp_filename(self, suffix=".wav"):
        """生成临时文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
        filename = f"audio_recording_{timestamp}{suffix}"
        return os.path.join(self.temp_dir, filename)
        
    def _check_audio_devices(self):
        """检查可用的音频设备"""
        try:
            devices = sd.query_devices()
            logger.info("可用的音频设备:")
            input_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    logger.info(f"  输入设备 {i}: {device['name']}")
                    input_devices.append(device)
            
            if not input_devices:
                logger.warning("未检测到可用的输入设备")
            else:
                logger.info(f"共检测到 {len(input_devices)} 个输入设备")
                # 设置默认设备
                default_device = sd.default.device[0]  # 默认输入设备
                logger.info(f"默认输入设备: {default_device}")
                
        except Exception as e:
            logger.warning(f"无法查询sounddevice设备: {str(e)}")
    
    def start_recording(self):
        """开始录音"""
        if self.is_recording:
            logger.warning("录音已在进行中")
            return
            
        logger.info("开始录音")
        self.is_recording = True
        self.recording_data = []
        
        # 启动录音线程
        self.recording_thread = threading.Thread(target=self._recording_worker)
        self.recording_thread.start()
        
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            logger.warning("录音未在进行中")
            return
            
        logger.info("停止录音")
        self.is_recording = False
        
        # 等待录音线程结束
        if self.recording_thread:
            self.recording_thread.join()
        
        # 保存录音数据到文件
        if self.recording_data:
            return self._save_recording_to_file()
        return None
    
    def _recording_worker(self):
        """录音工作线程"""
        try:
            def audio_callback(indata, frames, time, status):
                if status:
                    logger.warning(f"录音状态: {status}")
                if self.is_recording:
                    self.recording_data.append(indata.copy())
            
            # 开始录音流
            with sd.InputStream(
                samplerate=self.rate,
                channels=self.channels,
                dtype='int16',
                callback=audio_callback
            ):
                while self.is_recording:
                    sd.sleep(100)  # 100ms检查一次
                    
        except Exception as e:
            logger.error(f"录音过程中出错: {str(e)}")
            self.is_recording = False
    
    def _save_recording_to_file(self):
        """将录音数据保存到文件"""
        try:
            # 合并所有录音数据
            recording_array = np.concatenate(self.recording_data, axis=0)
            
            # 生成临时文件名
            temp_filename = self._generate_temp_filename()
            
            # 保存为WAV文件
            with wave.open(temp_filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 16位=2字节
                wf.setframerate(self.rate)
                wf.writeframes(recording_array.tobytes())
                
            logger.info(f"录音保存至: {temp_filename}")
            return temp_filename
            
        except Exception as e:
            logger.error(f"保存录音失败: {str(e)}")
            return None
        
    def record_audio(self, duration=5):
        """录制指定时长的音频（推荐方法）"""
        logger.info(f"录制{duration}秒音频")
        
        try:
            # 录制音频
            recording = sd.rec(
                int(duration * self.rate),
                samplerate=self.rate,
                channels=self.channels,
                dtype='int16'
            )
            sd.wait()  # 等待录音完成
            
            # 生成临时文件名
            temp_filename = self._generate_temp_filename()
            
            with wave.open(temp_filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 16位=2字节
                wf.setframerate(self.rate)
                wf.writeframes(recording.tobytes())
                
            logger.info(f"音频录制完成，保存至: {temp_filename}")
            return temp_filename
            
        except Exception as e:
            logger.error(f"录音失败: {str(e)}")
            raise
            
    def record_audio_to_array(self, duration=5):
        """录制音频并返回numpy数组（用于直接识别）"""
        logger.info(f"录制{duration}秒音频（数组格式）")
        
        try:
            # 录制音频
            recording = sd.rec(
                int(duration * self.rate),
                samplerate=self.rate,
                channels=self.channels,
                dtype='float32'  # Whisper需要float32格式
            )
            sd.wait()  # 等待录音完成
            
            # 转换为单声道
            if len(recording.shape) > 1:
                recording = recording.flatten()
                
            logger.info(f"音频录制完成，数组形状: {recording.shape}")
            return recording
            
        except Exception as e:
            logger.error(f"录音失败: {str(e)}")
            raise
        
    def transcribe(self, audio_input):
        """使用Whisper进行语音识别"""
        try:
            # 判断输入类型
            if isinstance(audio_input, str):
                # 文件路径
                logger.info(f"使用Whisper识别音频文件: {audio_input}")
                
                # 首先检查文件是否存在
                if not os.path.exists(audio_input):
                    logger.error(f"音频文件不存在: {audio_input}")
                    return ""
                
                # 检查文件大小
                file_size = os.path.getsize(audio_input)
                if file_size == 0:
                    logger.error(f"音频文件为空: {audio_input}")
                    return ""
                
                logger.info(f"音频文件大小: {file_size} 字节")
                
                # 尝试使用文件路径进行识别
                try:
                    result = self.model.transcribe(audio_input)
                    return result["text"]
                except Exception as file_error:
                    logger.warning(f"文件识别失败，尝试读取文件内容: {str(file_error)}")
                    
                    # 如果文件识别失败，尝试读取文件并转换为数组
                    try:
                        import librosa
                        audio_array, sr = librosa.load(audio_input, sr=self.rate)
                        logger.info(f"使用librosa读取音频，形状: {audio_array.shape}, 采样率: {sr}")
                        result = self.model.transcribe(audio_array)
                        return result["text"]
                    except ImportError:
                        logger.warning("librosa未安装，尝试使用wave读取")
                        
                        # 使用wave读取
                        try:
                            with wave.open(audio_input, 'rb') as wf:
                                frames = wf.readframes(wf.getnframes())
                                audio_array = np.frombuffer(frames, dtype=np.int16)
                                # 转换为float32并归一化
                                audio_array = audio_array.astype(np.float32) / 32768.0
                                logger.info(f"使用wave读取音频，形状: {audio_array.shape}")
                                result = self.model.transcribe(audio_array)
                                return result["text"]
                        except Exception as wave_error:
                            logger.error(f"wave读取失败: {str(wave_error)}")
                            return ""
                            
            elif isinstance(audio_input, np.ndarray):
                # numpy数组
                logger.info(f"使用Whisper识别音频数组，形状: {audio_input.shape}")
                result = self.model.transcribe(audio_input)
                return result["text"]
            else:
                logger.error(f"不支持的音频输入类型: {type(audio_input)}")
                return ""
                
        except Exception as e:
            logger.error(f"语音识别失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""
    
    def cleanup_temp_files(self, max_age_hours=24):
        """清理临时文件夹中的旧文件"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            for filename in os.listdir(self.temp_dir):
                if filename.startswith('audio_recording_') and filename.endswith('.wav'):
                    file_path = os.path.join(self.temp_dir, filename)
                    file_age = current_time - os.path.getmtime(file_path)
                    
                    if file_age > max_age_seconds:
                        try:
                            os.unlink(file_path)
                            cleaned_count += 1
                            logger.info(f"清理旧录音文件: {filename}")
                        except Exception as e:
                            logger.warning(f"删除文件失败: {filename}, 错误: {str(e)}")
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个旧录音文件")
                
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")
            
    def cleanup(self, temp_file=None):
        """清理资源"""
        if self.is_recording:
            self.stop_recording()
            
        if temp_file and os.path.exists(temp_file):
            os.unlink(temp_file)
            logger.info(f"临时文件已删除: {temp_file}")
            
        # 清理旧的临时文件
        self.cleanup_temp_files()
            
    def __del__(self):
        """析构函数，确保资源释放"""
        if self.is_recording:
            self.stop_recording()
        logger.info("AudioProcessor实例已释放") 