class GomokuGame {
    constructor() {
        this.canvas = document.getElementById('board');
        this.ctx = this.canvas.getContext('2d');
        this.boardSize = 15;
        this.cellSize = this.canvas.width / (this.boardSize + 1);
        this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(null));
        this.currentPlayer = 'black';
        this.gameOver = false;
        this.startTime = Date.now();
        this.timerInterval = null;
        this.winningLine = null;
        this.winAnimationFrame = null;
        
        this.init();
    }

    init() {
        this.drawBoard();
        this.setupEventListeners();
        this.startTimer();
        this.createWinPopup();
        this.bindWinPopupEvents();
    }

    drawBoard() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制棋盘背景
        this.ctx.fillStyle = '#DEB887';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制网格线
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i < this.boardSize; i++) {
            // 横线
            this.ctx.beginPath();
            this.ctx.moveTo(this.cellSize, this.cellSize * (i + 1));
            this.ctx.lineTo(this.cellSize * this.boardSize, this.cellSize * (i + 1));
            this.ctx.stroke();
            
            // 竖线
            this.ctx.beginPath();
            this.ctx.moveTo(this.cellSize * (i + 1), this.cellSize);
            this.ctx.lineTo(this.cellSize * (i + 1), this.cellSize * this.boardSize);
            this.ctx.stroke();
        }
        
        // 绘制棋子
        for (let i = 0; i < this.boardSize; i++) {
            for (let j = 0; j < this.boardSize; j++) {
                if (this.board[i][j]) {
                    this.drawPiece(i, j, this.board[i][j]);
                }
            }
        }

        // 绘制获胜线
        if (this.winningLine && this.gameOver) {
            this.drawWinningLine();
        }
    }

    drawPiece(row, col, color) {
        const x = this.cellSize * (col + 1);
        const y = this.cellSize * (row + 1);
        const radius = this.cellSize * 0.4;
        
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fillStyle = color;
        this.ctx.fill();
        this.ctx.strokeStyle = color === 'black' ? '#000' : '#666';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
    }

    drawWinningLine() {
        const {startRow, startCol, endRow, endCol} = this.winningLine;
        const startX = this.cellSize * (startCol + 1);
        const startY = this.cellSize * (startRow + 1);
        const endX = this.cellSize * (endCol + 1);
        const endY = this.cellSize * (endRow + 1);

        this.ctx.beginPath();
        this.ctx.moveTo(startX, startY);
        this.ctx.lineTo(endX, endY);
        this.ctx.strokeStyle = '#FF0000';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();
    }

    setupEventListeners() {
        this.canvas.addEventListener('click', (e) => this.handleClick(e));
        document.getElementById('newGame').addEventListener('click', () => this.newGame());
        document.getElementById('saveGame').addEventListener('click', () => this.saveGame());
        document.getElementById('loadGame').addEventListener('click', () => this.loadGame());
        
        // 为胜利弹窗添加事件监听器
        const popup = document.getElementById('winPopup');
        const closeBtn = document.getElementById('closeWinPopup');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                popup.classList.remove('show');
                this.newGame(); // 点击确定后开始新游戏
            });
        }
    }

    handleClick(e) {
        if (this.gameOver) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const col = Math.round(x / this.cellSize - 1);
        const row = Math.round(y / this.cellSize - 1);
        
        if (this.isValidMove(row, col)) {
            this.makeMove(row, col);
        }
    }

    isValidMove(row, col) {
        return row >= 0 && row < this.boardSize && 
               col >= 0 && col < this.boardSize && 
               !this.board[row][col];
    }

    makeMove(row, col) {
        this.board[row][col] = this.currentPlayer;
        this.drawBoard();
        
        if (this.checkWin(row, col)) {
            this.gameOver = true;
            this.showWinAnimation();
            this.stopTimer();
            return;
        }
        
        this.currentPlayer = this.currentPlayer === 'black' ? 'white' : 'black';
        this.updatePlayerInfo();
    }

    checkWin(row, col) {
        const directions = [
            [[0, 1], [0, -1]],  // 水平
            [[1, 0], [-1, 0]],  // 垂直
            [[1, 1], [-1, -1]], // 对角线
            [[1, -1], [-1, 1]]  // 反对角线
        ];
        
        for (const direction of directions) {
            let count = 1;
            let startRow = row;
            let startCol = col;
            let endRow = row;
            let endCol = col;
            
            for (const [dx, dy] of direction) {
                let r = row + dx;
                let c = col + dy;
                while (
                    r >= 0 && r < this.boardSize &&
                    c >= 0 && c < this.boardSize &&
                    this.board[r][c] === this.currentPlayer
                ) {
                    count++;
                    if (dx < 0 || (dx === 0 && dy < 0)) {
                        startRow = r;
                        startCol = c;
                    } else {
                        endRow = r;
                        endCol = c;
                    }
                    r += dx;
                    c += dy;
                }
            }
            
            if (count >= 5) {
                this.winningLine = {
                    startRow,
                    startCol,
                    endRow,
                    endCol
                };
                return true;
            }
        }
        return false;
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('timer').textContent = `${minutes}:${seconds}`;
        }, 1000);
    }

    stopTimer() {
        clearInterval(this.timerInterval);
    }

    updatePlayerInfo() {
        document.querySelector('.player.black').style.opacity = 
            this.currentPlayer === 'black' ? '1' : '0.5';
        document.querySelector('.player.white').style.opacity = 
            this.currentPlayer === 'white' ? '1' : '0.5';
    }

    showMessage(msg) {
        document.getElementById('message').textContent = msg;
    }

    newGame() {
        if (this.winAnimationFrame) {
            cancelAnimationFrame(this.winAnimationFrame);
            this.winAnimationFrame = null;
        }
        
        this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(null));
        this.currentPlayer = 'black';
        this.gameOver = false;
        this.startTime = Date.now();
        this.winningLine = null;
        
        const popup = document.getElementById('winPopup');
        if (popup) {
            popup.classList.remove('show');
        }
        
        this.drawBoard();
        this.updatePlayerInfo();
        this.showMessage('');
        this.startTimer();
    }

    saveGame() {
        const gameState = {
            board: this.board,
            currentPlayer: this.currentPlayer,
            startTime: this.startTime,
            gameOver: this.gameOver,
            winningLine: this.winningLine
        };
        localStorage.setItem('gomoku_save', JSON.stringify(gameState));
        this.showMessage('游戏已保存');
    }

    loadGame() {
        const savedState = localStorage.getItem('gomoku_save');
        if (savedState) {
            if (this.winAnimationFrame) {
                cancelAnimationFrame(this.winAnimationFrame);
                this.winAnimationFrame = null;
            }
            
            const gameState = JSON.parse(savedState);
            this.board = gameState.board;
            this.currentPlayer = gameState.currentPlayer;
            this.startTime = gameState.startTime;
            this.gameOver = gameState.gameOver;
            this.winningLine = gameState.winningLine;
            
            this.drawBoard();
            this.updatePlayerInfo();
            this.showMessage('游戏已加载');
            
            if (this.gameOver) {
                this.stopTimer();
                const winner = this.currentPlayer === 'black' ? '黑方' : '白方';
                this.showMessage(`${winner}获胜！`);
            } else {
                this.startTimer();
            }
        } else {
            this.showMessage('没有找到存档');
        }
    }
    
    createWinPopup() {
        // 检查是否已经存在弹窗
        if (document.getElementById('winPopup')) {
            return;
        }
        
        // 创建弹窗元素
        const popup = document.createElement('div');
        popup.id = 'winPopup';
        popup.className = 'win-popup';
        
        // 弹窗内容
        popup.innerHTML = `
            <div class="win-popup-content">
                <h2>恭喜获胜！</h2>
                <div class="win-message" id="winMessage"></div>
                <div class="confetti-container" id="confettiContainer"></div>
                <button id="closeWinPopup">确定</button>
            </div>
        `;
        
        // 添加到DOM
        document.body.appendChild(popup);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .win-popup {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1000;
                align-items: center;
                justify-content: center;
            }
            
            .win-popup.show {
                display: flex;
                animation: fadeIn 0.5s ease;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            .win-popup-content {
                background-color: white;
                padding: 2rem;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
                position: relative;
                overflow: hidden;
                animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }
            
            @keyframes popIn {
                from { transform: scale(0.5); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
            
            .win-popup h2 {
                color: #FF8C00;
                margin-bottom: 1rem;
                font-size: 2rem;
            }
            
            .win-message {
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }
            
            .confetti-container {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }
            
            .confetti {
                position: absolute;
                width: 8px;
                height: 16px;
                opacity: 0;
            }
            
            #closeWinPopup {
                padding: 0.5rem 2rem;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 1rem;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            
            #closeWinPopup:hover {
                background-color: #45a049;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    bindWinPopupEvents() {
        const popup = document.getElementById('winPopup');
        const closeBtn = document.getElementById('closeWinPopup');
        
        if (closeBtn) {
            const self = this;
            closeBtn.onclick = function() {
                popup.classList.remove('show');
                self.newGame();
            };
        }
    }
    
    showWinAnimation() {
        // 更新消息
        const winner = this.currentPlayer === 'black' ? '黑方' : '白方';
        this.showMessage(`${winner}获胜！`);
        
        // 显示获胜弹窗
        const popup = document.getElementById('winPopup');
        const winMessage = document.getElementById('winMessage');
        const confettiContainer = document.getElementById('confettiContainer');
        
        winMessage.textContent = `${winner}获胜！总用时：${document.getElementById('timer').textContent}`;
        popup.classList.add('show');
        
        // 确保每次显示弹窗时重新绑定事件
        this.bindWinPopupEvents();
        
        // 创建彩带元素
        confettiContainer.innerHTML = '';
        const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
        
        for (let i = 0; i < 100; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = `${Math.random() * 100}%`;
            confetti.style.top = `${Math.random() * 100}%`;
            confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
            confettiContainer.appendChild(confetti);
        }
        
        // 彩带动画
        const animateConfetti = () => {
            const confettis = document.querySelectorAll('.confetti');
            let allDone = true;
            
            confettis.forEach((confetti, index) => {
                const delay = index * 10;
                const duration = 1000 + Math.random() * 2000;
                const startTime = performance.now();
                
                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    
                    if (elapsed > delay) {
                        const progress = Math.min((elapsed - delay) / duration, 1);
                        const opacity = progress <= 0.7 ? progress / 0.7 : (1 - progress) / 0.3;
                        
                        confetti.style.opacity = opacity;
                        confetti.style.transform = `
                            translate(${(Math.random() - 0.5) * 300 * progress}px, ${200 * progress}px)
                            rotate(${Math.random() * 360 * progress}deg)
                        `;
                    }
                    
                    if (elapsed < delay + duration) {
                        allDone = false;
                    }
                    
                    return elapsed < delay + duration;
                };
                
                animate(performance.now());
            });
            
            this.winAnimationFrame = requestAnimationFrame(() => animateConfetti());
        };
        
        animateConfetti();
    }
}

// 初始化游戏
window.onload = () => {
    new GomokuGame();
}; 