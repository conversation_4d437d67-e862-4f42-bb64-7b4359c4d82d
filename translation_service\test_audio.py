#!/usr/bin/env python3
"""
测试AudioProcessor初始化
"""
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_audio_processor():
    """测试AudioProcessor初始化"""
    try:
        from audio_processor import AudioProcessor
        logger.info("开始测试AudioProcessor初始化...")
        
        # 创建AudioProcessor实例
        audio_processor = AudioProcessor(model_size="base")
        logger.info("AudioProcessor初始化成功！")
        
        # 测试sounddevice录音
        logger.info("测试sounddevice录音功能...")
        temp_file = audio_processor.record_audio(duration=1)  # 录音1秒测试
        logger.info(f"录音测试成功，文件: {temp_file}")
        
        # 清理
        audio_processor.cleanup(temp_file)
        logger.info("测试完成！")
        
        return True
        
    except Exception as e:
        logger.error(f"AudioProcessor测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_audio_processor()
    sys.exit(0 if success else 1) 