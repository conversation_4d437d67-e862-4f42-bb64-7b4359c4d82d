<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业计算器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="calculator">
        <div class="display">
            <div class="history" id="history"></div>
            <input type="text" id="result" class="result" readonly>
        </div>
        <div class="buttons">
            <div class="row">
                <button class="function-btn" onclick="clearAll()">AC</button>
                <button class="function-btn" onclick="clearEntry()">CE</button>
                <button class="function-btn" onclick="appendToExpression('%')">%</button>
                <button class="operation-btn" onclick="appendToExpression('/')">/</button>
            </div>
            <div class="row">
                <button class="number-btn" onclick="appendToExpression('7')">7</button>
                <button class="number-btn" onclick="appendToExpression('8')">8</button>
                <button class="number-btn" onclick="appendToExpression('9')">9</button>
                <button class="operation-btn" onclick="appendToExpression('*')">×</button>
            </div>
            <div class="row">
                <button class="number-btn" onclick="appendToExpression('4')">4</button>
                <button class="number-btn" onclick="appendToExpression('5')">5</button>
                <button class="number-btn" onclick="appendToExpression('6')">6</button>
                <button class="operation-btn" onclick="appendToExpression('-')">-</button>
            </div>
            <div class="row">
                <button class="number-btn" onclick="appendToExpression('1')">1</button>
                <button class="number-btn" onclick="appendToExpression('2')">2</button>
                <button class="number-btn" onclick="appendToExpression('3')">3</button>
                <button class="operation-btn" onclick="appendToExpression('+')">+</button>
            </div>
            <div class="row">
                <button class="number-btn" onclick="appendToExpression('0')">0</button>
                <button class="number-btn" onclick="appendToExpression('.')">.</button>
                <button class="function-btn" onclick="backspace()">⌫</button>
                <button class="equal-btn" onclick="calculate()">=</button>
            </div>
            <div class="row scientific-row">
                <button class="science-btn" onclick="calculateFunction('sqrt')">√</button>
                <button class="science-btn" onclick="calculateFunction('pow')">x²</button>
                <button class="science-btn" onclick="calculateFunction('sin')">sin</button>
                <button class="science-btn" onclick="calculateFunction('cos')">cos</button>
                <button class="science-btn" onclick="calculateFunction('tan')">tan</button>
            </div>
            <div class="row scientific-row">
                <button class="science-btn" onclick="calculateFunction('log')">log</button>
                <button class="science-btn" onclick="calculateFunction('ln')">ln</button>
                <button class="science-btn" onclick="appendToExpression('π')">π</button>
                <button class="science-btn" onclick="appendToExpression('e')">e</button>
                <button class="science-btn" onclick="appendToExpression('(')">(</button>
                <button class="science-btn" onclick="appendToExpression(')')">)</button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 