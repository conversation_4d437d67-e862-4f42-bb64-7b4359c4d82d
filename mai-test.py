from openai import OpenAI
from baidusearch.baidusearch import search
from datetime import datetime
import pytz

# Set the API key and base URL
client = OpenAI(base_url="https://api.deepseek.com", api_key="***********************************")
messages = []

def get_current_time():
    """获取当前北京时间"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    return datetime.now(beijing_tz)

def search_content(query):
    """通用搜索功能"""
    try:
        results = search(query, num_results=3)
        
        if not results:
            return []
        
        formatted_results = []
        for result in results:
            title = result.get('title', '').strip()
            abstract = result.get('abstract', '').strip()
            
            # 过滤无效结果
            if len(title) > 200 or "大家还在搜" in title or not title or not abstract:
                continue
                
            formatted_results.append(f"【{title}】{abstract}")
        
        return formatted_results[:3]
        
    except Exception as e:
        print(f"搜索出错: {e}")
        return []

def chat_with_ai():
    """主对话循环"""
    global messages  # 声明使用全局变量
    
    print("欢迎使用对话助手！输入'退出'结束对话。")
    
    while True:
        user_input = input("你: ")
        if user_input.lower() in ["退出", "exit"]:
            print("对话结束，再见！")
            break
            
        current_time = get_current_time()
        messages.append({"role": "user", "content": user_input})
        
        # 搜索相关内容
        search_results = search_content(user_input)
        
        # 构建系统提示
        system_content = f"""当前时间: {current_time.strftime('%Y年%m月%d日 %H:%M')} (北京时间)

重要要求：
1. 在回答任何问题时，请明确显示当前日期（{current_time.strftime('%Y年%m月%d日')}）
2. 如果涉及天气、新闻等时效性信息，必须在回答开头明确说明是"{current_time.strftime('%Y年%m月%d日')}"的信息
3. 基于当前准确时间回答问题

"""
        
        if search_results:
            system_content += f"搜索结果:\n{chr(10).join(search_results)}"
        
        messages.append({"role": "system", "content": system_content})
        
        # 调用AI
        try:
            response = client.chat.completions.create(
                model="deepseek-reasoner",
                messages=messages,
                temperature=0.3
            )
            
            assistant_reply = response.choices[0].message.content
            print(f"助手: {assistant_reply}")
            
            messages.append({"role": "assistant", "content": assistant_reply})
            
            # 限制对话历史长度，避免token过多
            if len(messages) > 10:
                messages = messages[-8:]
            
        except Exception as e:
            print(f"AI调用失败: {str(e)}")
            continue

if __name__ == "__main__":
    chat_with_ai()