// 获取DOM元素
const result = document.getElementById('result');
const history = document.getElementById('history');

// 常量定义
const PI = Math.PI;
const E = Math.E;

let expression = '';
let currentCalculation = '';

// 显示函数
function updateDisplay() {
    result.value = expression;
}

// 添加输入到表达式
function appendToExpression(value) {
    if (value === 'π') {
        expression += 'PI';
    } else if (value === 'e') {
        expression += 'E';
    } else {
        expression += value;
    }
    updateDisplay();
}

// 清除所有
function clearAll() {
    expression = '';
    currentCalculation = '';
    history.textContent = '';
    updateDisplay();
}

// 清除当前输入
function clearEntry() {
    expression = '';
    updateDisplay();
}

// 删除上一个字符
function backspace() {
    expression = expression.slice(0, -1);
    updateDisplay();
}

// 计算结果
function calculate() {
    try {
        // 替换特殊常量
        let evalExpression = expression.replace(/PI/g, Math.PI).replace(/E/g, Math.E);
        
        // 计算结果
        const evalResult = eval(evalExpression);
        
        // 更新历史记录
        currentCalculation = expression + ' = ' + evalResult;
        history.textContent = currentCalculation;
        
        // 更新当前表达式为结果
        expression = evalResult.toString();
        updateDisplay();
    } catch (error) {
        expression = '错误';
        updateDisplay();
        setTimeout(() => {
            expression = '';
            updateDisplay();
        }, 1500);
    }
}

// 科学计算函数
function calculateFunction(func) {
    try {
        let value;
        
        // 如果表达式为空，用0
        if (expression === '') {
            value = 0;
        } else {
            // 替换特殊常量并计算表达式的值
            let evalExpression = expression.replace(/PI/g, Math.PI).replace(/E/g, Math.E);
            value = eval(evalExpression);
        }
        
        let result;
        
        // 根据函数类型计算结果
        switch (func) {
            case 'sqrt':
                if (value < 0) throw new Error('负数不能开平方根');
                result = Math.sqrt(value);
                currentCalculation = `√(${expression}) = ${result}`;
                break;
            case 'pow':
                result = Math.pow(value, 2);
                currentCalculation = `(${expression})² = ${result}`;
                break;
            case 'sin':
                result = Math.sin(value);
                currentCalculation = `sin(${expression}) = ${result}`;
                break;
            case 'cos':
                result = Math.cos(value);
                currentCalculation = `cos(${expression}) = ${result}`;
                break;
            case 'tan':
                result = Math.tan(value);
                currentCalculation = `tan(${expression}) = ${result}`;
                break;
            case 'log':
                if (value <= 0) throw new Error('非正数不能求对数');
                result = Math.log10(value);
                currentCalculation = `log(${expression}) = ${result}`;
                break;
            case 'ln':
                if (value <= 0) throw new Error('非正数不能求自然对数');
                result = Math.log(value);
                currentCalculation = `ln(${expression}) = ${result}`;
                break;
            default:
                throw new Error('不支持的函数');
        }
        
        // 更新历史记录
        history.textContent = currentCalculation;
        
        // 更新当前表达式为结果
        expression = result.toString();
        updateDisplay();
    } catch (error) {
        expression = '错误';
        updateDisplay();
        setTimeout(() => {
            expression = '';
            updateDisplay();
        }, 1500);
    }
}

// 添加键盘支持
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    // 数字和运算符
    if (/[0-9]/.test(key) || ['+', '-', '*', '/', '.', '(', ')'].includes(key)) {
        appendToExpression(key);
    } 
    // 等于号或回车键
    else if (key === '=' || key === 'Enter') {
        calculate();
    } 
    // 退格键
    else if (key === 'Backspace') {
        backspace();
    } 
    // ESC键清除所有
    else if (key === 'Escape') {
        clearAll();
    }
});

// 初始化
updateDisplay(); 