"""
翻译服务模块
"""
import requests
import json
import logging
from enum import Enum
import os
import uuid
from typing import Dict, Optional

# 导入配置文件
try:
    from config import API_KEYS, DEFAULT_PROVIDER
except ImportError:
    # 如果没有配置文件，使用默认配置
    API_KEYS = {}
    DEFAULT_PROVIDER = "openai"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ApiProvider(Enum):
    TENCENT = "tencent"
    OPENAI = "openai"
    AZURE = "azure"
    BAIDU = "baidu"

class TranslationService:
    def __init__(self, provider: ApiProvider = ApiProvider.TENCENT, api_key: Optional[str] = None):
        """
        初始化翻译服务
        
        Args:
            provider: API提供商
            api_key: API密钥，如果为None则尝试从配置文件或环境变量获取
        """
        self.provider = provider
        self.api_key = api_key or self._get_api_key(provider)
        logger.info(f"初始化翻译服务，提供商: {provider.value}")
        
    def _get_api_key(self, provider: ApiProvider) -> Optional[str]:
        """获取API密钥，优先从配置文件，然后从环境变量"""
        # 首先尝试从配置文件获取
        config_key = self._get_api_key_from_config(provider)
        if config_key:
            return config_key
            
        # 然后尝试从环境变量获取
        return self._get_api_key_from_env(provider)
        
    def _get_api_key_from_config(self, provider: ApiProvider) -> Optional[str]:
        """从配置文件获取API密钥"""
        if not API_KEYS:
            return None
            
        provider_config = API_KEYS.get(provider.value)
        if not provider_config:
            return None
            
        if provider == ApiProvider.TENCENT:
            secret_id = provider_config.get("secret_id")
            secret_key = provider_config.get("secret_key")
            if secret_id and secret_key:
                logger.info("从配置文件获取腾讯云API密钥")
                return f"{secret_id}:{secret_key}"
        elif provider == ApiProvider.OPENAI:
            api_key = provider_config.get("api_key")
            if api_key:
                logger.info("从配置文件获取OpenAI API密钥")
                return api_key
        elif provider == ApiProvider.AZURE:
            key = provider_config.get("key")
            region = provider_config.get("region")
            if key and region:
                logger.info("从配置文件获取Azure API密钥")
                return f"{key}:{region}"
        elif provider == ApiProvider.BAIDU:
            app_id = provider_config.get("app_id")
            app_key = provider_config.get("app_key")
            if app_id and app_key:
                logger.info("从配置文件获取百度API密钥")
                return f"{app_id}:{app_key}"
                
        return None
        
    def _get_api_key_from_env(self, provider: ApiProvider) -> Optional[str]:
        """从环境变量获取API密钥"""
        env_vars = {
            ApiProvider.TENCENT: "TENCENT_API_KEY",
            ApiProvider.OPENAI: "OPENAI_API_KEY",
            ApiProvider.AZURE: "AZURE_API_KEY",
            ApiProvider.BAIDU: "BAIDU_API_KEY"
        }
        
        key = os.environ.get(env_vars.get(provider))
        if not key:
            logger.warning(f"未找到{provider.value}的API密钥环境变量")
        return key
        
    def translate(self, text: str, source_lang: str = "auto", target_lang: str = "zh") -> str:
        """
        翻译文本
        
        Args:
            text: 待翻译文本
            source_lang: 源语言，默认为自动检测
            target_lang: 目标语言，默认为中文
            
        Returns:
            翻译后的文本
        """
        if not text:
            return ""
            
        logger.info(f"翻译文本，源语言:{source_lang}，目标语言:{target_lang}")
        
        if self.provider == ApiProvider.OPENAI:
            return self._translate_with_openai(text, source_lang, target_lang)
        elif self.provider == ApiProvider.TENCENT:
            return self._translate_with_tencent(text, source_lang, target_lang)
        elif self.provider == ApiProvider.AZURE:
            return self._translate_with_azure(text, source_lang, target_lang)
        elif self.provider == ApiProvider.BAIDU:
            return self._translate_with_baidu(text, source_lang, target_lang)
        else:
            raise ValueError(f"不支持的API提供商: {self.provider}")
            
    def _translate_with_openai(self, text: str, source_lang: str, target_lang: str) -> str:
        """使用OpenAI API进行翻译"""
        try:
            import openai
            
            openai.api_key = self.api_key
            
            # 构建提示
            if source_lang == "auto":
                prompt = f"请将以下文本翻译成{target_lang}语言:\n\n{text}"
            else:
                prompt = f"请将以下{source_lang}文本翻译成{target_lang}语言:\n\n{text}"
                
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的翻译助手。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1024
            )
            
            translated_text = response.choices[0].message.content.strip()
            return translated_text
            
        except Exception as e:
            logger.error(f"OpenAI翻译失败: {str(e)}")
            return f"[翻译错误] {str(e)}"
            
    def _translate_with_tencent(self, text: str, source_lang: str, target_lang: str) -> str:
        """使用腾讯云API进行翻译"""
        try:
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
            from tencentcloud.tmt.v20180321 import tmt_client, models
            
            # 从环境变量或者传入参数获取腾讯云API密钥
            secret_id, secret_key = self.api_key.split(':')
            
            cred = credential.Credential(secret_id, secret_key)
            httpProfile = HttpProfile()
            httpProfile.endpoint = "tmt.tencentcloudapi.com"
            
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            client = tmt_client.TmtClient(cred, "ap-guangzhou", clientProfile)
            
            # 构建请求
            req = models.TextTranslateRequest()
            
            # 转换语言代码
            tencent_lang_map = {
                "zh": "zh",
                "en": "en",
                "ja": "jp",
                "ko": "kr",
                "fr": "fr",
                "es": "es",
                "auto": "auto"
            }
            
            source = tencent_lang_map.get(source_lang, source_lang)
            target = tencent_lang_map.get(target_lang, target_lang)
            
            params = {
                "SourceText": text,
                "Source": source,
                "Target": target,
                "ProjectId": 0
            }
            
            req.from_json_string(json.dumps(params))
            resp = client.TextTranslate(req)
            
            return resp.TargetText
            
        except TencentCloudSDKException as err:
            logger.error(f"腾讯云翻译失败: {str(err)}")
            return f"[翻译错误] {str(err)}"
        except Exception as e:
            logger.error(f"腾讯云翻译失败: {str(e)}")
            return f"[翻译错误] {str(e)}"
            
    def _translate_with_azure(self, text: str, source_lang: str, target_lang: str) -> str:
        """使用Azure API进行翻译"""
        try:
            # 获取Azure Translator API密钥和地区
            key, region = self.api_key.split(':')
            
            # 配置请求
            endpoint = "https://api.cognitive.microsofttranslator.com"
            path = '/translate'
            constructed_url = endpoint + path
            
            # 构建请求参数
            params = {
                'api-version': '3.0',
                'to': target_lang
            }
            
            if source_lang != "auto":
                params['from'] = source_lang
                
            headers = {
                'Ocp-Apim-Subscription-Key': key,
                'Ocp-Apim-Subscription-Region': region,
                'Content-type': 'application/json',
                'X-ClientTraceId': str(uuid.uuid4())
            }
            
            # 构建请求体
            body = [{
                'text': text
            }]
            
            # 发送请求
            response = requests.post(constructed_url, params=params, headers=headers, json=body)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            return result[0]["translations"][0]["text"]
            
        except Exception as e:
            logger.error(f"Azure翻译失败: {str(e)}")
            return f"[翻译错误] {str(e)}"
            
    def _translate_with_baidu(self, text: str, source_lang: str, target_lang: str) -> str:
        """使用百度翻译API进行翻译"""
        try:
            import hashlib
            import random
            
            # 百度翻译API
            url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
            
            # 获取百度翻译API密钥
            app_id, app_key = self.api_key.split(':')
            
            # 构建请求参数
            salt = str(random.randint(32768, 65536))
            sign = hashlib.md5((app_id + text + salt + app_key).encode()).hexdigest()
            
            # 语言代码映射
            baidu_lang_map = {
                "zh": "zh",
                "en": "en",
                "ja": "jp",
                "ko": "kor",
                "fr": "fra",
                "es": "spa",
                "auto": "auto"
            }
            
            source = baidu_lang_map.get(source_lang, source_lang)
            target = baidu_lang_map.get(target_lang, target_lang)
            
            # 构建请求
            params = {
                'q': text,
                'from': source,
                'to': target,
                'appid': app_id,
                'salt': salt,
                'sign': sign
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            if 'trans_result' in result:
                translated_text = ' '.join([item['dst'] for item in result['trans_result']])
                return translated_text
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"百度翻译API错误: {error_msg}")
                return f"[翻译错误] {error_msg}"
                
        except Exception as e:
            logger.error(f"百度翻译失败: {str(e)}")
            return f"[翻译错误] {str(e)}" 